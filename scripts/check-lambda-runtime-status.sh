#!/bin/bash

# Quick Lambda Runtime Status Check
# This script checks the current runtime version of your Lambda functions

set -e

# Configuration
LAMBDA_FUNCTION_NAME="hmbl-sms-reminders-handler-prod"
AWS_REGION="us-east-2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔍 Lambda Runtime Status Check"
echo "=============================="
echo ""

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    log_error "AWS CLI is not installed"
    exit 1
fi

# Check AWS credentials
log_info "Checking AWS credentials..."
if ! aws sts get-caller-identity &> /dev/null; then
    log_error "AWS CLI is not configured or credentials are invalid"
    exit 1
fi
log_success "AWS credentials are valid"

# Check Lambda function
log_info "Checking Lambda function: $LAMBDA_FUNCTION_NAME"

if ! aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" &> /dev/null; then
    log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found"
    exit 1
fi

# Get function details
runtime=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Runtime' --output text)
state=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'State' --output text)
last_modified=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'LastModified' --output text)
timeout=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Timeout' --output text)
memory=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'MemorySize' --output text)

echo ""
log_info "Lambda Function Details:"
echo "  Function Name: $LAMBDA_FUNCTION_NAME"
echo "  Runtime: $runtime"
echo "  State: $state"
echo "  Last Modified: $last_modified"
echo "  Timeout: ${timeout}s"
echo "  Memory: ${memory}MB"

echo ""

# Check runtime status
case $runtime in
    "nodejs20.x")
        log_success "✨ Runtime is up to date! Using Node.js 20"
        log_info "Node.js 20 is supported until April 30, 2026"
        ;;
    "nodejs22.x")
        log_success "🚀 Runtime is using the latest! Using Node.js 22"
        log_info "Node.js 22 is supported until April 30, 2027"
        ;;
    "nodejs18.x")
        log_warning "⚠️  Runtime needs upgrade! Using deprecated Node.js 18"
        log_warning "Node.js 18 support ends September 1, 2025"
        echo ""
        log_info "To upgrade, run: ./scripts/upgrade-lambda-nodejs-runtime.sh"
        ;;
    *)
        log_warning "Unknown or unsupported runtime: $runtime"
        ;;
esac

# Check function state
case $state in
    "Active")
        log_success "Function is active and ready"
        ;;
    "Pending")
        log_warning "Function is still updating..."
        ;;
    "Failed")
        log_error "Function is in failed state"
        state_reason=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'StateReason' --output text)
        echo "  Reason: $state_reason"
        ;;
    *)
        log_warning "Unknown function state: $state"
        ;;
esac

echo ""

# Quick test option
if [ "$state" = "Active" ]; then
    echo "💡 Want to test the function? Run:"
    echo "   aws lambda invoke --function-name $LAMBDA_FUNCTION_NAME --payload '{}' /tmp/test-response.json"
fi

echo ""
log_info "Status check complete!"
