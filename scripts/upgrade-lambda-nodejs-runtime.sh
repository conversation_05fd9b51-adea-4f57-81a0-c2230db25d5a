#!/bin/bash

# Lambda Node.js Runtime Upgrade Script
# This script upgrades existing Lambda functions from Node.js 18 to Node.js 20

set -e

# Configuration
LAMBDA_FUNCTION_NAME="hmbl-sms-reminders-handler-prod"
NEW_RUNTIME="nodejs20.x"
AWS_REGION="us-east-2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if AWS CLI is configured
check_aws_cli() {
    log_info "Checking AWS CLI configuration..."
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
    log_success "AWS CLI is configured"
}

# Get current Lambda function information
get_current_runtime() {
    log_info "Getting current Lambda function runtime..."
    
    if ! aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" &> /dev/null; then
        log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found"
        exit 1
    fi
    
    local current_runtime=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Runtime' --output text)
    local current_timeout=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Timeout' --output text)
    local current_memory=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'MemorySize' --output text)
    
    log_info "Current configuration:"
    echo "  Runtime: $current_runtime"
    echo "  Timeout: ${current_timeout}s"
    echo "  Memory: ${current_memory}MB"
    
    if [ "$current_runtime" = "$NEW_RUNTIME" ]; then
        log_success "Lambda function is already using $NEW_RUNTIME"
        exit 0
    elif [ "$current_runtime" != "nodejs18.x" ]; then
        log_warning "Lambda function is using $current_runtime (not nodejs18.x)"
        echo "This script is designed to upgrade from nodejs18.x to $NEW_RUNTIME"
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 0
        fi
    fi
}

# Wait for Lambda function to be ready
wait_for_lambda_ready() {
    local function_name=$1
    local max_attempts=30
    local attempt=1
    
    log_info "Waiting for Lambda function to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        local state=$(aws lambda get-function --function-name "$function_name" --query 'Configuration.State' --output text 2>/dev/null)
        
        if [ "$state" = "Active" ]; then
            log_success "Lambda function is ready!"
            return 0
        elif [ "$state" = "Pending" ]; then
            log_info "Lambda function is still updating... (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
        elif [ "$state" = "Failed" ]; then
            log_error "Lambda function update failed"
            aws lambda get-function --function-name "$function_name" --query 'Configuration.StateReason' --output text
            return 1
        else
            log_warning "Unknown state: $state (attempt $attempt/$max_attempts)"
            sleep 5
            ((attempt++))
        fi
    done
    
    log_error "Timeout waiting for Lambda function to be ready"
    return 1
}

# Update Lambda runtime
update_runtime() {
    log_info "Updating Lambda runtime to $NEW_RUNTIME..."
    
    aws lambda update-function-configuration \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --runtime "$NEW_RUNTIME"
    
    if ! wait_for_lambda_ready "$LAMBDA_FUNCTION_NAME"; then
        log_error "Failed to update Lambda runtime"
        exit 1
    fi
    
    log_success "Lambda runtime updated successfully!"
}

# Verify the update
verify_update() {
    log_info "Verifying the runtime update..."
    
    local updated_runtime=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Runtime' --output text)
    
    if [ "$updated_runtime" = "$NEW_RUNTIME" ]; then
        log_success "Runtime successfully updated to $NEW_RUNTIME"
    else
        log_error "Runtime update failed. Current runtime: $updated_runtime"
        exit 1
    fi
}

# Test the function
test_function() {
    log_info "Testing the updated Lambda function..."
    
    # Create test payload
    cat > /tmp/test-payload.json << EOF
{
  "phoneNumber": "+15551234567",
  "message": "Runtime upgrade test - $(date)",
  "reminderType": "test"
}
EOF
    
    log_info "Invoking Lambda function with test payload..."
    local response=$(aws lambda invoke \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --payload file:///tmp/test-payload.json \
        --cli-binary-format raw-in-base64-out \
        /tmp/lambda-response.json)
    
    local status_code=$(echo "$response" | jq -r '.StatusCode')
    
    if [ "$status_code" = "200" ]; then
        log_success "Lambda function test successful!"
        log_info "Response:"
        cat /tmp/lambda-response.json | jq .
    else
        log_error "Lambda function test failed with status code: $status_code"
        log_error "Response:"
        cat /tmp/lambda-response.json
        exit 1
    fi
    
    # Cleanup
    rm -f /tmp/test-payload.json /tmp/lambda-response.json
}

# Main execution
main() {
    echo "🚀 Lambda Node.js Runtime Upgrade Script"
    echo "========================================="
    echo "Function: $LAMBDA_FUNCTION_NAME"
    echo "Target Runtime: $NEW_RUNTIME"
    echo "Region: $AWS_REGION"
    echo ""
    
    check_aws_cli
    get_current_runtime
    
    echo ""
    log_warning "This will update your Lambda function runtime from Node.js 18 to Node.js 20"
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        exit 0
    fi
    
    update_runtime
    verify_update
    test_function
    
    echo ""
    log_success "🎉 Lambda runtime upgrade completed successfully!"
    log_info "Your Lambda function is now using Node.js 20 and is ready for the Node.js 18 deprecation"
}

# Run the script
main "$@"
