<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create #(sectionDisplayName) Item - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #E97100;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #6B7280;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Graphik', sans-serif;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #FD8205;
            box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
        }
        
        .form-input::placeholder {
            color: #9CA3AF;
        }
        
        .color-input-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .color-picker {
            width: 50px;
            height: 44px;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            cursor: pointer;
            background: white;
        }
        
        .color-picker::-webkit-color-swatch-wrapper {
            padding: 0;
        }
        
        .color-picker::-webkit-color-swatch {
            border: none;
            border-radius: 6px;
        }
        
        .form-help {
            font-size: 12px;
            color: #6B7280;
            margin-top: 4px;
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex: 1;
        }
        
        .btn-primary {
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .btn-secondary {
            background: #E5E7EB;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background: #D1D5DB;
        }
        
        .preview-section {
            margin-top: 20px;
            padding: 16px;
            background: #F9FAFB;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
        }
        
        .preview-label {
            font-size: 12px;
            font-weight: 600;
            color: #6B7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .preview-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #E5E7EB;
        }
        
        .preview-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            border: 1px solid #E5E7EB;
        }
        
        .preview-text {
            font-size: 14px;
            color: #374151;
        }
        
        .preview-key {
            font-size: 12px;
            color: #6B7280;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Create #(sectionDisplayName) Item</h1>
            <p>Add a new item to the #(sectionDisplayName) dropdown</p>
        </div>
        
        <form method="POST" action="/org/#(orgID)/constants/#(section)/create">
            <div class="form-group">
                <label for="title" class="form-label">Title *</label>
                <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    class="form-input" 
                    placeholder="Enter item title"
                    required
                    oninput="updatePreview()"
                >
                <div class="form-help">The display name for this item</div>
            </div>
            
            <div class="form-group">
                <label for="key" class="form-label">Key</label>
                <input 
                    type="text" 
                    id="key" 
                    name="key" 
                    class="form-input" 
                    placeholder="Auto-generated from title"
                    oninput="updatePreview()"
                >
                <div class="form-help">Unique identifier (leave blank to auto-generate from title)</div>
            </div>
            
            #if(supportsColor):
            <div class="form-group">
                <label for="color" class="form-label">Color</label>
                <div class="color-input-group">
                    <input 
                        type="color" 
                        id="color" 
                        name="color" 
                        class="color-picker"
                        value="#FD8205"
                        oninput="updatePreview()"
                    >
                    <input 
                        type="text" 
                        id="colorText" 
                        class="form-input" 
                        placeholder="#FD8205"
                        style="flex: 1;"
                        oninput="updateColorFromText()"
                    >
                </div>
                <div class="form-help">Choose a color for this tag</div>
            </div>
            #endif
            
            <div class="preview-section">
                <div class="preview-label">Preview</div>
                <div class="preview-item" id="preview">
                    #if(supportsColor):
                    <div class="preview-color" id="previewColor" style="background-color: #FD8205;"></div>
                    #endif
                    <span class="preview-text" id="previewText">Enter a title to see preview</span>
                    <span class="preview-key" id="previewKey">auto_generated_key</span>
                </div>
            </div>
            
            <div class="form-actions">
                <a href="/org/#(orgID)" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Create Item</button>
            </div>
        </form>
    </div>
    
    <script>
        function generateKey(title) {
            return title.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+|_+$/g, '');
        }
        
        function updatePreview() {
            const title = document.getElementById('title').value;
            const keyInput = document.getElementById('key');
            const colorInput = document.getElementById('color');
            const colorTextInput = document.getElementById('colorText');
            
            const previewText = document.getElementById('previewText');
            const previewKey = document.getElementById('previewKey');
            const previewColor = document.getElementById('previewColor');
            
            // Update preview text
            previewText.textContent = title || 'Enter a title to see preview';
            
            // Update preview key
            const key = keyInput.value || generateKey(title);
            previewKey.textContent = key || 'auto_generated_key';
            
            // Update preview color if supported
            if (previewColor && colorInput) {
                const color = colorInput.value;
                previewColor.style.backgroundColor = color;
                colorTextInput.value = color;
            }
        }
        
        function updateColorFromText() {
            const colorText = document.getElementById('colorText').value;
            const colorInput = document.getElementById('color');
            const previewColor = document.getElementById('previewColor');
            
            if (colorText.match(/^#[0-9A-Fa-f]{6}$/)) {
                colorInput.value = colorText;
                if (previewColor) {
                    previewColor.style.backgroundColor = colorText;
                }
            }
        }
        
        // Initialize preview
        document.addEventListener('DOMContentLoaded', function() {
            updatePreview();
        });
    </script>
</body>
</html>
