{"name": "sms-reminder-handler", "version": "1.0.0", "description": "AWS Lambda function for sending SMS reminders via Twilio", "main": "index.js", "scripts": {"test": "node test.js", "package": "zip -r sms-reminder-handler.zip index.js package.json", "deploy": "aws lambda update-function-code --function-name sms-reminder-handler --zip-file fileb://sms-reminder-handler.zip"}, "keywords": ["aws", "lambda", "sms", "twi<PERSON>", "healthcare", "reminders"], "author": "HMBL Healthcare", "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=20.0.0"}}