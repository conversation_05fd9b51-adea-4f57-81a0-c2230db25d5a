//
//  OrgDropdownController.swift
//  
//
//  Created by <PERSON> on 7/28/25.
//

import Foundation
import Vapor
import Fluent

struct OrgDropdownController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let orgRoutes = routes.grouped("org", ":orgID")
        
        // Main dashboard route
        orgRoutes.get(use: dashboard)
        
        // Create item routes
        orgRoutes.get("constants", ":section", "create", use: showCreateForm)
        orgRoutes.post("constants", ":section", "create", use: createItem)
        
        // Remove all items route
        orgRoutes.post("constants", ":section", "remove-all", use: removeAllItems)
        
        // Individual item operations (using item key instead of ID)
        orgRoutes.delete("constants", ":section", ":itemID", use: deleteItem)
        orgRoutes.put("constants", ":section", ":itemID", use: updateItem)
    }
    
    // MARK: - Dashboard
    func dashboard(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString) else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        // Verify user has access to this organization
        return try verifyOrgAccess(req: req, orgID: orgID).flatMap { _ in
            // Fetch organization with meta data
            return Organization.find(orgID, on: req.db)
                .flatMapThrowing { org in
                    guard let org = org else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }

                    let constants = org.meta?.constants ?? [:]

                    // Create context for the template
                    let context = DashboardContext(
                        orgID: orgIDString,
                        sections: DropdownSection.allCases.map { section in
                            let sectionItems = constants[section.rawValue] ?? []
                            return SectionData(
                                key: section.rawValue,
                                displayName: section.displayName,
                                supportsColor: section.supportsColor,
                                items: sectionItems
                            )
                        }
                    )

                    return context
                }
                .flatMap { context in
                    return req.view.render("org-dropdown-dashboard", context)
                }
        }
    }
    
    // MARK: - Create Item
    func showCreateForm(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let section = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyOrgAccess(req: req, orgID: orgID).flatMap { _ in
            let context = CreateItemContext(
                orgID: orgIDString,
                section: section.rawValue,
                sectionDisplayName: section.displayName,
                supportsColor: section.supportsColor
            )
            
            return req.view.render("org-dropdown-create", context)
        }
    }
    
    func createItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let _ = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        return try verifyAdminAccess(req: req, orgID: orgID).flatMapThrowing { _ in
            let input = try req.content.decode(CreateDropdownItemInput.self)

            let newItem = PickerItem(
                title: input.title,
                key: input.generateKey(),
                color: input.color
            )

            return newItem
        }.flatMap { newItem in
            return Organization.find(orgID, on: req.db)
                .flatMapThrowing { org in
                    guard let org = org else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }

                    // Initialize meta if it doesn't exist
                    if org.meta == nil {
                        org.meta = OrgMetaData(constants: [:], features: [])
                    }

                    // Add the new item to the section
                    var constants = org.meta?.constants ?? [:]
                    var sectionItems = constants[sectionString] ?? []
                    sectionItems.append(newItem)
                    constants[sectionString] = sectionItems
                    org.meta?.constants = constants

                    return org
                }
                .flatMap { org in
                    return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
                }
        }
    }
    
    // MARK: - Remove All Items
    func removeAllItems(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let _ = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        return try verifyAdminAccess(req: req, orgID: orgID).flatMap { _ in
            return Organization.find(orgID, on: req.db)
                .flatMapThrowing { org in
                    guard let org = org else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }

                    // Remove all items from the section
                    var constants = org.meta?.constants ?? [:]
                    constants[sectionString] = []
                    org.meta?.constants = constants

                    return org
                }
                .flatMap { org in
                    return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
                }
        }
    }
    
    // MARK: - Delete Individual Item
    func deleteItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let itemKey = req.parameters.get("itemID") else {
            req.logger.error("Delete item - Invalid parameters: orgID=\(req.parameters.get("orgID") ?? "nil"), section=\(req.parameters.get("section") ?? "nil"), itemID=\(req.parameters.get("itemID") ?? "nil")")
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        req.logger.info("Delete item - orgID: \(orgIDString), section: \(sectionString), itemKey: \(itemKey)")

        return try verifyAdminAccess(req: req, orgID: orgID).flatMap { _ in
            return Organization.find(orgID, on: req.db)
                .flatMapThrowing { org in
                    guard let org = org else {
                        req.logger.error("Delete item - Organization not found: \(orgIDString)")
                        throw Abort(.notFound, reason: "Organization not found")
                    }

                    req.logger.info("Delete item - Found org: \(org.title)")

                    // Remove the item with the specified key from the section
                    var constants = org.meta?.constants ?? [:]
                    var sectionItems = constants[sectionString] ?? []
                    let originalCount = sectionItems.count
                    sectionItems.removeAll { $0.key == itemKey }
                    constants[sectionString] = sectionItems
                    org.meta?.constants = constants

                    req.logger.info("Delete item - Removed \(originalCount - sectionItems.count) items from section \(sectionString)")

                    return org
                }
                .flatMap { org in
                    return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
                }
        }
    }
    
    // MARK: - Update Item
    func updateItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let itemKey = req.parameters.get("itemID") else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        return try verifyAdminAccess(req: req, orgID: orgID).flatMapThrowing { _ in
            let input = try req.content.decode(UpdateDropdownItemInput.self)
            return input
        }.flatMap { input in
            return Organization.find(orgID, on: req.db)
                .flatMapThrowing { org in
                    guard let org = org else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }

                    // Find and update the item with the specified key
                    var constants = org.meta?.constants ?? [:]
                    var sectionItems = constants[sectionString] ?? []

                    if let index = sectionItems.firstIndex(where: { $0.key == itemKey }) {
                        if let title = input.title {
                            sectionItems[index].title = title
                        }
                        if let color = input.color {
                            sectionItems[index].color = color
                        }
                        if let key = input.key {
                            sectionItems[index].key = key
                        }
                        constants[sectionString] = sectionItems
                        org.meta?.constants = constants
                    }

                    return org
                }
                .flatMap { org in
                    return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
                }
        }
    }

    // MARK: - Helper Methods
    private func verifyOrgAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }
            return ()
        }
    }

    private func verifyAdminAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            req.logger.info("verifyAdminAccess - User: \(user.email), UserOrgID: \(user.$org.id?.uuidString ?? "nil"), RequestedOrgID: \(orgID.uuidString)")

            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                req.logger.error("verifyAdminAccess - Access denied: user org \(user.$org.id?.uuidString ?? "nil") != requested org \(orgID.uuidString)")
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }

            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("drop_all") ||
                  roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") })
            else {
                req.logger.error("verifyAdminAccess - Admin access required: user roles = \(user.roles ?? [])")
                throw Abort(.forbidden, reason: "Admin access required")
            }

            req.logger.info("verifyAdminAccess - Success for user \(user.email)")
            return ()
        }
    }
}

// MARK: - Context Structs
struct DashboardContext: Content {
    let orgID: String
    let sections: [SectionData]
}

struct SectionData: Content {
    let key: String
    let displayName: String
    let supportsColor: Bool
    let items: [PickerItem]
}

struct CreateItemContext: Content {
    let orgID: String
    let section: String
    let sectionDisplayName: String
    let supportsColor: Bool
}

// MARK: - Input Structs
struct CreateDropdownItemInput: Content {
    let title: String
    let color: String?
    let key: String?

    func generateKey() -> String {
        if let providedKey = key, !providedKey.isEmpty {
            return providedKey
        }
        return title.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-z0-9_]", with: "", options: .regularExpression)
    }
}

struct UpdateDropdownItemInput: Content {
    let title: String?
    let color: String?
    let key: String?
}

// MARK: - Supported Sections
enum DropdownSection: String, CaseIterable {
    case roles = "roles"
    case noteTags = "noteTags"
    case taskTypes = "taskTypes"
    case memberTags = "memberTags"
    case carePackageSections = "carePackageSections"
    case taskCompletionReasons = "taskCompletionReasons"

    var displayName: String {
        switch self {
        case .roles:
            return "Roles"
        case .noteTags:
            return "Note Tags"
        case .taskTypes:
            return "Task Types"
        case .memberTags:
            return "Member Tags"
        case .carePackageSections:
            return "Care Package Sections"
        case .taskCompletionReasons:
            return "Task Completion Reasons"
        }
    }

    var supportsColor: Bool {
        switch self {
        case .noteTags, .memberTags:
            return true
        default:
            return false
        }
    }
}
