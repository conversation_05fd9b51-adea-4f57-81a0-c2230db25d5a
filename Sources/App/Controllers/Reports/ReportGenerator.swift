//
//  File.swift
//  
//
//  Created by <PERSON> on 8/25/23.
//

import Foundation
import Vapor
import Fluent
import Leaf
import SwiftCSV
import SotoCore
import SotoS3
import ZIPFoundation

let emptyReport = ReportConfiguration(orgID: "", endpoint: "", bucket: "", path: "", base: "")

struct ReportConfiguration {
    var orgID: String
    var endpoint: String
    var bucket: String
    var exportAll: Bool = false
    var path: String
    var base: String
    
    func endpointWithBucket() -> String {
        return "https://\(endpoint)\(bucket)"
    }
    
    func folder() -> String {
        return String(bucket.split(separator: "/").last ?? "")
    }
    
    func downloadLink() -> String {
        return "https://\(base).s3.amazonaws.com/\(path)/\(folder()).zip"
    }
    
    func isWellup() -> Bool {
        return base.lowercased() == WellupReportBuilder.bucket
    }
    
    func compressFileName() -> String {
        return "compress-\(Int(Date().timeIntervalSince1970))"
    }
}



enum ReportCreator: Codable {
    case genesis
    case empowered
    case none
    case allData(orgId:String, reportDate: String)
    case wellup(orgId:String, reportDate: String)
    
    static func configuration(type: ReportCreator) -> ReportConfiguration {
        switch type {
        case .genesis:
            return ReportConfiguration(
//                https://w5xztu42bf.execute-api.us-east-2.amazonaws.com/dev/genesis-ru/testUpload.zip
            orgID: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee",
            endpoint: "w5xztu42bf.execute-api.us-east-2.amazonaws.com/dev/",
            bucket: "genesis-ru",
            exportAll: true, //we need to make sure this is on for 3rd party clients.
            path: "reports",
            base: "genesis")
//        https://f1mh0knq84.execute-api.us-east-2.amazonaws.com/dev/empowered-data/testUpload.zip
        case .empowered:
            return ReportConfiguration(
            orgID: "fc312a94-9382-4909-8e08-7c8076ff9f68",
            endpoint: "f1mh0knq84.execute-api.us-east-2.amazonaws.com/dev/",
            bucket: "empowered-data",
            exportAll: true, //we need to make sure this is on for 3rd party clients.
            path: "reports",
            base: "empowered")
            
        case .wellup(let orgID, let reportDate):
            return ReportConfiguration(
            orgID: orgID, //"f7f9780d-4fb1-44e7-9965-93a6f584d6ee"
            endpoint: "628s0m6dx0.execute-api.us-east-2.amazonaws.com/stg/",
            bucket: "\(WellupReportBuilder.bucket)/\(WellupReportBuilder.env)/\(orgID.lowercased())/all/\(reportDate)",
            path: "\(WellupReportBuilder.env)/\(orgID.lowercased())/all/\(reportDate)",
            base: WellupReportBuilder.bucket)
            
        case .none:
            return emptyReport
//            https://wellup-reports.s3.us-east-2.amazonaws.com/staging/
        case .allData(let orgId, let reportDate):
            return ReportConfiguration(
            orgID: orgId, //"f7f9780d-4fb1-44e7-9965-93a6f584d6ee"
            endpoint: "628s0m6dx0.execute-api.us-east-2.amazonaws.com/stg/",
            bucket: "\(WellupReportBuilder.bucket)/\(WellupReportBuilder.env)/\(orgId.lowercased())/all/\(reportDate)",
            exportAll: true,
            path: "\(WellupReportBuilder.env)/\(orgId.lowercased())/all/\(reportDate)",
            base: WellupReportBuilder.bucket)
        }
    }
}

struct ReportGenerator {
    
    func runReportSchedule(req: Request, input: CSVInput) -> EventLoopFuture<ClientResponse> {
        let reportConfig = ReportCreator.configuration(type: .wellup(orgId: "", reportDate: input.combineDate()))
        //        let dateFormatter:DateFormatter = DateFormatter()
        //        dateFormatter.dateFormat = "yyyy-MM-dd"
        //                let input = try req.content.decode(CSVInput.self)
        //        guard let startDate = dateFromYearMonthDayString(date:input.startDate) else { throw  Abort(.badRequest, reason: "Start Date Is Required.")}
        //        guard let endDate   = dateFromYearMonthDayString(date: input.endDate)  else { throw  Abort(.badRequest, reason: "End Date Is Required.")}
        
        return try! self.orgLookup(req: req, id: reportConfig.orgID).flatMap { org in
            
            var csvText =  self.buildTestHeaders()
            
            for member in org.members {
                csvText.append(buildRow(member: member))
            }
            //if we paramatize the file name lets strip out the .csv if its passed
            return try! self.generateCSV(fileName: "", csvText: csvText, req: req, isEmpty: org
                .members.count == 0, config: reportConfig)
        }
    }
    
    fileprivate func orgLookup(req: Request, id: String) throws -> EventLoopFuture<Organization> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .organization) }
        return Organization.query(on: req.db).filter(\.$id == networkID)
            .with(\.$users)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$members)
            .with(\.$teams)
            .with(\.$networks)
            .with(\.$services)
            .with(\.$households)
            .with(\.$chats).first().flatMapThrowing { org in
                guard let foundModel = org else { throw NetworkError.error(type: .organization) }
                return foundModel
            }
    }
    
    
    fileprivate func sendToAPIGateway(req:Request, jsonData:ByteBuffer?, fileName:String, config: ReportConfiguration) -> EventLoopFuture<ClientResponse> {
        guard let data = jsonData else {
            return req.eventLoop.future(ClientResponse(status: .badRequest))
        }
        let s3 = S3(client: req.aws.client, region: .useast2)
        
        return s3.putObject(.init(
            body:.byteBuffer(data),
            bucket: config.bucket,
            key: fileName)).flatMap { bucket in
                return req.eventLoop.future(ClientResponse(status: .accepted))
        }
    }
    
    fileprivate func sendToAPIGatewayREST(req:Request, jsonData:ByteBuffer?, fileName:String, config: ReportConfiguration) -> EventLoopFuture<ClientResponse> {
        guard let data = jsonData else {
            return req.eventLoop.future(ClientResponse(status: .badRequest))
        }
        
        let client = req.client
        let url = queryURL(urlString: "\(config.endpointWithBucket())/\(fileName)")
        let uri = URI(string: url!)
        
        return client.put(uri, headers: [
            "Content-type":"text/csv",
        ], beforeSend: { req in
            print(req)
            req.body = data //ByteBuffer.init(data: data)
        }).map { response in
            print(response)
            return response
        }
    }
    
    fileprivate func getDownloadFolder(req:Request, config: ReportConfiguration) -> EventLoopFuture<String> {
        let s3 = S3(client: req.aws.client, region: .useast2)
//        let getObjectRequest = S3.GetObjectRequest(bucket: config.bucket, key: config.folder())
        let urlString = "https://\(config.bucket).s3.amazonaws.com/\(config.folder())"
        return s3.signURL(url: URL(string: urlString)!,
                          httpMethod: .GET, expires: .hours(24)).map { url in
            return url.absoluteString
        }
    }
    
    fileprivate func buildRow(member:Member) -> String {
        return "\(member.email),\(member.firstName),\(member.middleName ?? ""),\(member.lastName),\(member.type),\(member.roles),\(member.dob),\(member.color ?? ""),\(member.gender ?? ""),\(member.genderIdentity ?? ""),\(member.ethnicity ?? ""),\(member.sexualIdentity ?? ""),\(member.pronouns ?? ""),\(member.lang ?? ""),\(member.referredBy ?? ""),\(member.lastAt ?? ""),\(member.status ?? ""),\(member.score ?? ""),\(Date.createdAtString(date: member.createdAt)),\(Date.createdAtString(date: member.updatedAt))\n"
    }
    
    fileprivate func buildTestHeaders() -> String {
        return "email,firstName,middleName,lastName,type,roles,dob,color,gender,ethnicity,sexualIdentity,genderIdentity,pronouns,lang,referredBy,lastAt,status,score,created at,updated at\n"
    }
    
    fileprivate func rootDirectory(app:Application) -> URL {
        return URL(fileURLWithPath: app.directory.publicDirectory +  "csv/", isDirectory: true)
//        return URL(fileURLWithPath: app.directory.workingDirectory +  "Sources/App/csv/", isDirectory: true)
    }
    
    func createZipFile(req:Request, fileName:String, from fileURLs: [URL]) throws -> EventLoopFuture<URL> {
        let tempDir = self.rootDirectory(app: req.application)
        let zipFileURL = tempDir.appendingPathComponent("\(fileName).zip")
        let fileManager = FileManager.default
        let archive = try Archive(url: zipFileURL, accessMode: .create)
        
        for fileURL in fileURLs {
            try archive.addEntry(with: fileURL.lastPathComponent, relativeTo: tempDir)
            if fileManager.fileExists(atPath: fileURL.relativePath) {
                try fileManager.removeItem(atPath: fileURL.relativePath)
            }            
        }
        req.logger.info("SAVE zipFileURL: \(zipFileURL)")
        req.logger.warning("SAVE zipFileURL: \(zipFileURL)")
        return req.eventLoop.future(zipFileURL)
    }
    
    func uploadToS3(req:Request, zipFileURL: URL, config: ReportConfiguration) -> EventLoopFuture<ClientResponse> {
        req.logger.info("GET zipFileURL: \(zipFileURL)")
        req.logger.warning("GET zipFileURL: \(zipFileURL)")
        return req.fileio.collectFile(at: zipFileURL.relativePath).flatMap { buffer in
            return sendToAPIGateway(req: req, jsonData: buffer, fileName: "\(config.folder()).zip", config: config).flatMap { response in
                let fileManager = FileManager.default
                if fileManager.fileExists(atPath: zipFileURL.relativePath) {
                    try? fileManager.removeItem(atPath: zipFileURL.relativePath)
                }
                return req.eventLoop.future(response)
            }
        }
    }
    
    func generateAndCollectCSV(fileName:String, csvText:String, req:Request, isEmpty:Bool, filePaths:[URL]) throws -> EventLoopFuture<[URL]> {
        var allPaths = filePaths
        if isEmpty {
            return req.eventLoop.makeSucceededFuture(
                []
            )
        } else {
            let fileNameWithTime = "\(fileName)_\(Int(Date().timeIntervalSince1970)).csv"
            let path = self.rootDirectory(app: req.application).appendingPathComponent(fileNameWithTime)
            
            return req.fileio.writeFile(ByteBuffer(string: csvText), at: path.relativePath).flatMap {
                allPaths.append(path)
                return req.eventLoop.future(allPaths)
            }
        }
    }
            
    func generateCSV(fileName:String, csvText:String, req:Request, isEmpty:Bool, config:ReportConfiguration) throws -> EventLoopFuture<ClientResponse> {
        if isEmpty {
            return req.eventLoop.makeSucceededFuture(
                ClientResponse(status: .badRequest)
            )
        } else {
            let fileNameWithTime = "\(fileName)_\(Int(Date().timeIntervalSince1970)).csv"
            let path = self.rootDirectory(app: req.application).appendingPathComponent(fileNameWithTime)
            
            return req.fileio.writeFile(ByteBuffer(string: csvText), at: path.relativePath).flatMap {
                return req.fileio.collectFile(at: path.relativePath).flatMap { buffer in
                    return sendToAPIGateway(req: req, jsonData: buffer, fileName: fileNameWithTime, config: config).flatMap { response in
                        let fileManager = FileManager.default
                        if fileManager.fileExists(atPath: path.relativePath) {
                            try? fileManager.removeItem(atPath: path.relativePath)
                        }
                        return req.eventLoop.future(response)
                    }
                }
            }
        }
    }
    
    func generateLocalCSV(fileName:String, csvText:String, req:Request, isEmpty:Bool) throws -> EventLoopFuture<ClientResponse> {
        if isEmpty {
            return req.eventLoop.makeSucceededFuture(
                ClientResponse(status: .badRequest)
            )
        } else {
            let fileNameWithTime = "\(fileName)_\(Int(Date().timeIntervalSince1970)).csv"
            let path = self.rootDirectory(app: req.application).appendingPathComponent(fileNameWithTime)
            
            return req.fileio.writeFile(ByteBuffer(string: csvText), at: path.relativePath).transform(to: ClientResponse(status: .accepted))
        }
    }
    
    func createCSVDirectoryIfNeeded(in directory: String) throws {
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: directory) {
            try fileManager.createDirectory(atPath: directory, withIntermediateDirectories: true, attributes: nil)
        }
    }
}
