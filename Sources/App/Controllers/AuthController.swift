//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent

import SotoCore
import SotoCognitoIdentityProvider

//import QRCode

enum UserError {
    case usernameTaken
}

struct MFALoginInput: Content {
    var password:   String
    var username:   String
}

struct AuthUpdateInput: Content {
    static var param = "userID"
    var password:   String
    var email:      String
}


extension UserError: AbortError {
    var description: String {
        reason
    }
    
    var status: HTTPResponseStatus {
        switch self {
        case .usernameTaken: return .conflict
        }
    }
    
    var reason: String {
        switch self {
        case .usernameTaken: return "Email already taken"
        }
    }
}


extension AuthUpdateInput: Validatable {
    static func validations(_ validations: inout Validations) {
        validations.add("email", as: String.self, is: !.empty)
        validations.add("password", as: String.self, is: .count(6...))
    }
}

extension MFALoginInput: Validatable {
    static func validations(_ validations: inout Validations) {
        validations.add("email", as: String.self, is: !.empty)
        validations.add("password", as: String.self, is: .count(6...))
    }
}

extension UserSignup: Validatable {
    static func validations(_ validations: inout Validations) {
        validations.add("username", as: String.self, is: !.empty)
        validations.add("password", as: String.self, is: .count(6...))
    }
}


extension MemberSignup: Validatable {
    static func validations(_ validations: inout Validations) {
        validations.add("username", as: String.self, is: !.empty)
        validations.add("password", as: String.self, is: .count(6...))
    }
}

struct AuthController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let usersRoute = routes.grouped("auth")
        usersRoute.post("signup", use: create)
        usersRoute.post("memberSignup", use: memberCreate)
        
        let tokenProtected = usersRoute.grouped(Token.authenticator())
        tokenProtected.get("session", use: getMyOwnUser)
        
        
//        let passwordResetProtected = usersRoute.grouped()
        usersRoute.put([":userID","passwordrest"], use: update)
        
        let passwordProtected = usersRoute.grouped(AuthUser.authenticator())
        passwordProtected.post("login", use: login)
        passwordProtected.post("memberLogin", use: memberLogin)
        
        passwordProtected.post("mfaCreateUser", use: mfaCreateUser)
        passwordProtected.post("mfaLogin", use: mfaLogin)
        passwordProtected.post("mfaVerify", use: mfaVerify)
        passwordProtected.post("mfaNewPwdChallenge", use: mfaNewPwdChallenge)
        passwordProtected.post("startMFASetup", use: startMFASetup)
        
    }
    
    
    fileprivate func update(req: Request) throws -> EventLoopFuture<NewSession> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        try AuthUpdateInput.validate(content: req)
        let userSignup = try req.content.decode(AuthUpdateInput.self)
        let newPassword = try AuthUser.hashPassword(from: userSignup.password)
        var token: Token!
        
        return try! UsersController.find(req: req, id: id).flatMap { user in
            
            return checkAndReturnIfUserExists(userSignup.email, req: req).flatMap {  authUser in
                guard let user = authUser else {
                    return req.eventLoop.future(error: Abort(.badRequest))
                }
                user.passwordHash = newPassword
                
                return user.save(on: req.db).transform(to: user).flatMap { authUser in
                    guard let newToken = try? authUser.createToken(source: .signup) else {
                        return req.eventLoop.future(error: Abort(.internalServerError))
                    }
                    token = newToken
                    return token.save(on: req.db)
                }.flatMapThrowing {
                    NewSession(token: token.value, user: try user.asPublic())
                }
            }
        }
    }
    
    
    fileprivate func memberCreate(req: Request) throws -> EventLoopFuture<CreateMemberSession> {
        try MemberSignup.validate(content: req)
        let memberSignup = try req.content.decode(MemberSignup.self)
        let user = try AuthUser.create(from: memberSignup)
        var token: Token!
        return try! OrgsController.find(req: req, id: memberSignup.orgID).flatMap { org in
            
            return checkIfUserExists(memberSignup.username, req: req).flatMap { exists in
                guard !exists else {
                    return req.eventLoop.future(error: UserError.usernameTaken)
                }
                return user.save(on: req.db)
            }
            .flatMap {
                guard let newToken = try? user.createToken(source: .signup) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                token = newToken
                return token.save(on: req.db)
            }
            .flatMap {
                guard let member = try? MembersController.createWith(req: req, authUser: user, signup: memberSignup, org: org) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                return member
            }
            .flatMap {
                return try! MembersController.findAuth(req: req, auth: user.asPublic().id.uuidString).flatMap { member in
                    
                    return try! TwilioController.fireEmail(req: req,
                                                           input: EmailTemplateMember(to: member.email,
                                                                                         first: member.firstName,
                                                                                         username: member.email,
                                                                                         pwd: memberSignup.password,
                                                                                         tempID: EmailTemplate.memberInvite,
                                                                                         iosURL: appDownload()).json()).flatMap({ res in
                        return req.eventLoop.future(CreateMemberSession(token: token.value, member: member))
                    })
                }
            }
        }
    }
    
    
    fileprivate func create(req: Request) throws -> EventLoopFuture<CreateSession> {
        try UserSignup.validate(content: req)
        let userSignup = try req.content.decode(UserSignup.self)
        let user = try AuthUser.create(from: userSignup)
        var token: Token!
        return try! OrgsController.find(req: req, id: userSignup.orgID).flatMap { org in
            
            return checkIfUserExists(userSignup.username, req: req).flatMap { exists in
                guard !exists else {
                    return req.eventLoop.future(error: UserError.usernameTaken)
                }
                return user.save(on: req.db)
            }
            .flatMap {
                guard let newToken = try? user.createToken(source: .signup) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                token = newToken
                return token.save(on: req.db)
            }
            .flatMap {
                guard let user = try? UsersController.createWith(req: req, authUser: user, signup: userSignup, org: org) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                return user
            }
            .flatMap {
                return try! UsersController.findAuth(req: req, auth: user.asPublic().id.uuidString).flatMap { user in
                    
                    return try! TwilioController.fireEmail(req: req,
                                                           input: EmailTemplateNavigator(to: userSignup.username,
                                                                                         first: userSignup.firstName,
                                                                                         username: userSignup.username,
                                                                                         pwd: userSignup.password,
                                                                                         tempID: EmailTemplate.navigatorInvite,
                                                                                         url: loginUrl(),
                                                                                         iosURL: appDownload()).json()).flatMap({ res in 
                        return req.eventLoop.future(CreateSession(token: token.value, user: user))
                    })
                }
            }
        }
    }
    
    static func createTempSignup(req: Request, userSignup:UserSignup, user: AuthUser) throws -> EventLoopFuture<CreateSession> {
        var token: Token!
        return try! OrgsController.find(req: req, id: userSignup.orgID).flatMap { org in
            
            return checkIfUserExists(userSignup.username, req: req).flatMap { exists in
                guard !exists else {
                    return req.eventLoop.future(error: UserError.usernameTaken)
                }
                return user.save(on: req.db)
            }
            .flatMap {
                guard let newToken = try? user.createToken(source: .signup) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                token = newToken
                return token.save(on: req.db)
            }
            .flatMap {
                guard let user = try? UsersController.createWith(req: req, authUser: user, signup: userSignup, org: org) else {
                    return req.eventLoop.future(error: Abort(.internalServerError))
                }
                return user
            }
            .flatMap {
                return try! UsersController.findAuth(req: req, auth: user.asPublic().id.uuidString).flatMap { user in
                    return req.eventLoop.future(CreateSession(token: token.value, user: user))
                }
            }
        }
    }
    
    fileprivate func memberLogin(req: Request) throws -> EventLoopFuture<LoginMemberResponse> {
        let user  = try req.auth.require(AuthUser.self)
        let token = try user.createToken(source: .login)
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .logins)
        
        return token.save(on: req.db).flatMapThrowing {
            NewSession(token: token.value, user: try user.asPublic())
        }.flatMap { newSession in
            return try! MembersController.findAuth(req: req, auth: user.id?.uuidString ?? "").flatMap { member in
                return cloudwatch.putLog(message: logLogin(userId: user.id, isMember: true), on: req.eventLoop).flatMap { _ in
                    return req.eventLoop.future(LoginMemberResponse(token: newSession.token, member: member))
                }
            }
        }
    }
    
    fileprivate func login(req: Request) throws -> EventLoopFuture<LoginResponse> {
        let user  = try req.auth.require(AuthUser.self)
        let token = try user.createToken(source: .login)
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .logins)
        
        return token.save(on: req.db).flatMapThrowing {
            NewSession(token: token.value, user: try user.asPublic())
        }.flatMap { newSession in
            return try! UsersController.findAuth(req: req, auth: user.id?.uuidString ?? "").flatMap { user in
                return cloudwatch.putLog(message: logLogin(userId: user.id), on: req.eventLoop).flatMap { _ in
                    return req.eventLoop.future(LoginResponse(token: newSession.token, user: user))
                }
            }
        }
    }
    
    func getMyOwnUser(req: Request) throws -> EventLoopFuture<User> {
        let auth  = try req.auth.require(AuthUser.self)
        return try! UsersController.findAuth(req: req, auth: auth.id?.uuidString ?? "").flatMap { user in
            return req.eventLoop.future(user)
        }
    }
    
    static func userFrom(_ authUser: AuthUser, req: Request) -> EventLoopFuture<User> {
        return try! UsersController.findAuth(req: req, auth: authUser.id?.uuidString ?? "")
    }
    
    private func checkIfUserExists(_ username: String, req: Request) -> EventLoopFuture<Bool> {
        AuthUser.query(on: req.db)
            .filter(\.$username == username)
            .first()
            .map { $0 != nil }
    }
    
    static func checkIfUserExists(_ username: String, req: Request) -> EventLoopFuture<Bool> {
        AuthUser.query(on: req.db)
            .filter(\.$username == username)
            .first()
            .map { $0 != nil }
    }
    
    static func authUserFrom(_ username: String, req: Request) -> EventLoopFuture<AuthUser?> {
        AuthUser.query(on: req.db)
            .filter(\.$username == username)
            .first()
    }
    
    private func checkAndReturnIfUserExists(_ username: String, req: Request) -> EventLoopFuture<AuthUser?> {
        AuthUser.query(on: req.db)
            .filter(\.$username == username)
            .first()
    }
    
    private func loginUrl() -> String {
        return isProduction ? "https://dona-workspace-prod.duploapps.dona.health/login" : "https://dona-workspace-stag01.duploapps.dona.health/login"
    }
    
    private func appDownload() -> String {
        return isProduction ? "https://apps.apple.com/us/app/wellup-navigator/id6469057243" : "https://testflight.apple.com/join/yLDw3AP7"
    }
    
    static func userFromToken(req: Request) throws -> EventLoopFuture<User> {
        let token = try extractBearerToken(from: req)
        return try! lookup(token: token, req: req).flatMap({ uuid in
            return try! UsersController.findAuth(req: req, auth: uuid.uuidString)
        })
    }
    
    static func userIdFromToken(req: Request) throws -> EventLoopFuture<UUID> {
        let token = try extractBearerToken(from: req)
        return try! lookup(token: token, req: req)
    }
    
    static func lookup(token:String, req: Request) throws -> EventLoopFuture<UUID> {
        return Token.query(on: req.db).filter(\.$value == token).with(\.$user).first().flatMapThrowing { model in
            guard let foundModel = model else {  throw NetworkError.error(type: .token) }
            guard let uuid = foundModel.user.id else {  throw NetworkError.error(type: .token) }
            return uuid
        }
    }
}

//MARK: - AWS MFA
extension AuthController {
    
    fileprivate func mfaCreateUser(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        let input = try req.content.decode(UserSignup.self)
        return try AuthController().create(req: req).flatMap { session in
            return try! CognitoMFAService.createCognitoUser(req: req,
                                                            email: input.username,
                                                            pwd: input.password).flatMap { response in
                return try! CognitoMFAService.initiateAuth(req: req,
                                                           username: input.username,
                                                           password: input.password,
                                                           userPoolId: "us-east-2_IxHPkSZps",
                                                           clientId: "42lpmcrsc9fnhg81a0aeptm96",
                                                           token: session.token)
            }
        }
    }
    
    fileprivate func mfaLogin(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        let login = try req.content.decode(MFALoginInput.self)
        let user  = try req.auth.require(AuthUser.self)
        let token = try user.createToken(source: .login)
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .logins)
        
        return token.save(on: req.db).flatMapThrowing {
            NewSession(token: token.value, user: try user.asPublic())
        }.flatMap { newSession in
            return try! UsersController.findAuth(req: req, auth: user.id?.uuidString ?? "").flatMap { user in
                return cloudwatch.putLog(message: logLogin(userId: user.id), on: req.eventLoop).flatMap { _ in
                    return try! CognitoMFAService.initiateAuth(req: req,
                                                               username: user.email,
                                                               password: login.password,
                                                               userPoolId: "us-east-2_IxHPkSZps",
                                                               clientId: "42lpmcrsc9fnhg81a0aeptm96",
                                                               token: token.value)
                }
            }
        }
    }
    
    fileprivate func mfaNewPwdChallenge(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        let input = try req.content.decode(ChallengeRequest.self)
        return try CognitoMFAService.respondToNewPasswordChallenge(req: req, input: input)
    }
    
    fileprivate func startMFASetup(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        return try AuthController.userFromToken(req: req).flatMap { user in
            return try! CognitoMFAService.startMFASetup(req: req).flatMapThrowing({ response in
                return try AuthController.buildQrCode(response: response, name: user.fullName())
            })
        }
    }
    
    fileprivate static func buildQrCode(response: MFAAuthResponse, name: String) throws -> MFAAuthResponse {
        var response = response
        let otpauth = "otpauth://totp/\(name)?secret=\(response.secretCode ?? "")&issuer=Wellup"
        print(otpauth)
        
//        guard let doc = try? QRCode.Document(utf8String: otpauth) else {
//            throw Abort(.internalServerError, reason: "Failed to generate QR Document")
//        }
        
//        guard let pngData = try? doc.pngData(dimension: 100) else {
//            throw Abort(.internalServerError, reason: "Failed to generate QR Code")
//        }
        
//        let base64 = pngData.base64EncodedString()
//        response.qrCode = "data:image/png;base64,\(base64)"
        response.qrCode = ""
        return response
    }
    
    fileprivate func buildQRCode(req: Request, name: String) throws -> EventLoopFuture<MFAAuthResponse> {
        return try! CognitoMFAService.startMFASetup(req: req).flatMapThrowing({ response in
            var response = response
            let otpauth = "otpauth://totp/\(name)?secret=\(response.secretCode ?? "")&issuer=Wellup"
            print(otpauth)
            
//            guard let doc = try? QRCode.Document(utf8String: otpauth) else {
//                throw Abort(.internalServerError, reason: "Failed to generate QR Document")
//            }
//            
//            guard let pngData = try? doc.pngData(dimension: 100) else {
//                throw Abort(.internalServerError, reason: "Failed to generate QR Code")
//            }
//            
//            let base64 = pngData.base64EncodedString()
//            response.qrCode = "data:image/png;base64,\(base64)"
            response.qrCode = ""
            return response
        })
    }
    
    fileprivate func mfaVerify(req: Request) throws -> EventLoopFuture<LoginResponse> {
        let input = try req.content.decode(VerifyMFARequest.self)
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .logins)
        return AuthController.authUserFrom(input.username, req: req).flatMap { authUser in
            
            guard let authUser = authUser else {
                return req.eventLoop.future(error: Abort(.badRequest, reason: "User not found"))
            }
            
            guard let newToken = try? authUser.createToken(source: .login) else {
                return req.eventLoop.future(error: Abort(.badRequest, reason: "User not found"))
            }
            
            print(authUser.username)
            
            return try! CognitoMFAService.mfaVerify(req: req).flatMap({ response in
                
                return try! UsersController.findAuth(req: req, auth: authUser.id?.uuidString ?? "").flatMap { user in
                    
                    return cloudwatch.putLog(message: logLogin(userId: user.id), on: req.eventLoop).flatMap { _ in
                        
                        return req.eventLoop.future(LoginResponse(token: newToken.value, user: user))
                    }
                }
            })
        }
    }
}

//MARK: - CloudWatchLogs
extension AuthController {
    fileprivate func logLogin(userId: UUID?, isMember: Bool = false) -> String {
        return CloudWatchLogMessage.send(msg: .logins(user: userId?.uuidString ?? "-", status: "success", member: isMember))
        
    }
}


struct MFAVerifyRequest: Content {
    let email: String
    let mfaCode: String
    let session: String //tmp
}

struct MFAAuthResponse: Content {
    var challengeName: String?
    var session: String?
    var challengeParameters: [String: String]?
    var accessToken: String?
    var token: String? //wellup token.
    
    //mfa
    var secretCode: String?
    var qrCode: String? //base64
}

struct VerifyMFARequest: Content {
    let username: String
    let session: String
    let oneTimePassword: String
}

struct ChallengeRequest: Content {
    let username: String
    let newPassword: String
    let session: String
}

struct CognitoMFAService {
//    us-east-2_IxHPkSZps
    
//    app info
//    client_id: 42lpmcrsc9fnhg81a0aeptm96
    //client secret: 14274fop9vnsfo0rl7reoti4r4odd2q5ng5fckn71236ra0nb3m0
    
    static func calculateSecretHash(clientId: String, clientSecret: String, username: String) -> String {
        let key = SymmetricKey(data: Data(clientSecret.utf8))
        let message = "\(username)\(clientId)"
        let signature = HMAC<SHA256>.authenticationCode(for: Data(message.utf8), using: key)
        return Data(signature).base64EncodedString()
    }
    
    //Kickoff
    static func initiateAuth(req: Request, username: String, password: String, userPoolId: String, clientId: String, token: String) throws -> EventLoopFuture<MFAAuthResponse> {
        let client = CognitoIdentityProvider(client: req.aws.client)
        let secretHash = CognitoMFAService.calculateSecretHash(clientId: clientId,
                                                               clientSecret: "14274fop9vnsfo0rl7reoti4r4odd2q5ng5fckn71236ra0nb3m0",
                                                               username: username)
        let request = CognitoIdentityProvider.InitiateAuthRequest(
            authFlow: .userPasswordAuth,
            authParameters: ["USERNAME": username,
                             "PASSWORD": password,
                             "SECRET_HASH": secretHash],
            clientId: clientId
        )
        
        return client.initiateAuth(request).flatMap { response in
            let challengeName = response.challengeName?.description ?? ""
            let session = response.session ?? ""
            let challengeParameters = response.challengeParameters
            let accessToken = response.authenticationResult?.accessToken
            print(challengeName)
            if challengeName == "MFA_SETUP" {
                //retun session
                return try! startMFASetupWithSession(req: req, session: session).flatMapThrowing({ response in
                    return try AuthController.buildQrCode(response: response, name: username)
                })
            }
            else if challengeName == "SOFTWARE_TOKEN_MFA" {
                //retun session and challenge name
                return req.eventLoop.future(MFAAuthResponse(challengeName: challengeName, session: session))
            }
            else if challengeName == "NEW_PASSWORD_REQUIRED" {
                //return session
                return req.eventLoop.future(MFAAuthResponse(challengeName: challengeName, session: session, challengeParameters: challengeParameters, accessToken: accessToken, token: token))
//                return req.eventLoop.future(MFAAuthResponse(challengeName: challengeName, session: session, challengeParameters: challengeParameters, accessToken: accessToken, token: token)).flatMap { response in
//                    let input = ChallengeRequest(username: username, newPassword: password, session: response.session ?? "")
//                    return try! respondToNewPasswordChallenge(req: req, input: input).flatMap({ response in
//                        return try! initiateAuth(req: req, username: username, password: password, userPoolId: userPoolId, clientId: clientId, token: token)
//                    })
//                }
            }
            else {
                return req.eventLoop.future(MFAAuthResponse(challengeName: challengeName, session: session, challengeParameters: challengeParameters, accessToken: accessToken, token: token))
            }
        }
    }
        
    static func mfaVerify(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        return try CognitoMFAService.respondToMFAChallenge(req: req)
    }
    
    static func respondToNewPasswordChallenge(req: Request, input: ChallengeRequest) throws -> EventLoopFuture<MFAAuthResponse> {
        let clientId = "42lpmcrsc9fnhg81a0aeptm96"
        let clientSecret = "14274fop9vnsfo0rl7reoti4r4odd2q5ng5fckn71236ra0nb3m0"

        // Calculate SECRET_HASH
        let secretHash = CognitoMFAService.calculateSecretHash(clientId: clientId, clientSecret: clientSecret, username: input.username)

        let challengeResponse = CognitoIdentityProvider.RespondToAuthChallengeRequest(
            challengeName: .newPasswordRequired,
            challengeResponses: [
                "USERNAME": input.username,
                "NEW_PASSWORD": input.newPassword,
                "SECRET_HASH": secretHash
            ], clientId: clientId,
            session: input.session
        )

        // Create AWS client
        let client = req.aws.client
        let cognitoIdentityProvider = CognitoIdentityProvider(client: client)

        return cognitoIdentityProvider.respondToAuthChallenge(challengeResponse).map { response in
            return MFAAuthResponse(
                challengeName: response.challengeName?.rawValue,
                session: response.session
            )
        }
    }

    static func respondToMFAChallenge(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        let input = try req.content.decode(VerifyMFARequest.self)
        //need to setup client /  secrete env
        //need to setup user pool for stg and prd.
        
        let clientId = "42lpmcrsc9fnhg81a0aeptm96"
        let clientSecret = "14274fop9vnsfo0rl7reoti4r4odd2q5ng5fckn71236ra0nb3m0"

        let secretHash = CognitoMFAService.calculateSecretHash(clientId: clientId, clientSecret: clientSecret, username: input.username)
        
        let challengeResponse = CognitoIdentityProvider.RespondToAuthChallengeRequest(
            challengeName: .softwareTokenMfa,
            challengeResponses: [
                "USERNAME": input.username,
                "SOFTWARE_TOKEN_MFA_CODE": input.oneTimePassword,
                "SECRET_HASH": secretHash
            ], clientId: clientId,
            session: input.session // Pass the correct session here
        )
        
        let cognitoIdentityProvider = CognitoIdentityProvider(client: req.aws.client)
        return cognitoIdentityProvider.respondToAuthChallenge(challengeResponse).flatMapThrowing { response in
            
            guard let accessToken = response.authenticationResult?.accessToken else {
                throw Abort(.unauthorized, reason: "Authentication failed or session expired.")
            }
            
            let challengeName = response.challengeName?.description ?? ""
            let session = response.session ?? ""
            let challengeParameters = response.challengeParameters
            
            return MFAAuthResponse(challengeName: challengeName,
                                   session: session,
                                   challengeParameters: challengeParameters,
                                   accessToken: accessToken)
        }
    }
    
    
    static func startMFASetup(req: Request) throws -> EventLoopFuture<MFAAuthResponse> {
        struct StartMFARequest: Content {
            let session: String
        }
        let input = try req.content.decode(StartMFARequest.self)
        return try startMFASetupWithSession(req: req, session: input.session)
    }
    
    static func startMFASetupWithSession(req: Request, session: String) throws -> EventLoopFuture<MFAAuthResponse> {
        let associateRequest = CognitoIdentityProvider.AssociateSoftwareTokenRequest(
            session: session
        )

        // Create AWS client
        let client = req.aws.client
        let cognitoIdentityProvider = CognitoIdentityProvider(client: client)

        return cognitoIdentityProvider.associateSoftwareToken(associateRequest).map { response in
            return MFAAuthResponse(session: response.session, secretCode: response.secretCode)
        }
    }
    
    //MARK Create User
    static func createCognitoUser(req: Request, email: String, pwd: String) throws -> EventLoopFuture<CognitoIdentityProvider.AdminCreateUserResponse> {
        let cognitoIdentityProvider = CognitoIdentityProvider(client: req.aws.client)
//        let userSignup = try req.content.decode(AuthUpdateInput.self)
        
        let request = CognitoIdentityProvider.AdminCreateUserRequest(
            desiredDeliveryMediums:[.email],
            messageAction: .suppress,
            temporaryPassword: pwd,
            userAttributes: [
                .init(name: "email", value: email),
                .init(name: "email_verified", value: "true")
            ],
            username: email,
            userPoolId: "us-east-2_IxHPkSZps"
        )
        
        return cognitoIdentityProvider.adminCreateUser(request)
    }
    
    // Enable MFA for the user
    static func enableMFA(username: String, provider: CognitoIdentityProvider) -> EventLoopFuture<CognitoIdentityProvider.AdminSetUserMFAPreferenceResponse> {
        
        let request = CognitoIdentityProvider.AdminSetUserMFAPreferenceRequest(softwareTokenMfaSettings: .init(enabled: true,
                                                                                                               preferredMfa: true),
                                                                               username: username,
                                                                               userPoolId: "us-east-2_IxHPkSZps")
        return provider.adminSetUserMFAPreference(request)
    }
}
