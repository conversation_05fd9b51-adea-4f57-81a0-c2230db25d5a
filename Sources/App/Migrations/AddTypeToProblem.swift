//
//  AddTypeToProblem.swift
//
//
//  Created by Augment Agent on 8/12/25.
//

import Foundation
import Fluent
import Vapor

struct AddTypeToProblem: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .field("type", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .deleteField("type")
            .update()
    }
}
