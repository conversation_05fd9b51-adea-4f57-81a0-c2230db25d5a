//
//  SMSReminderLogger.swift
//  
//
//  Created by <PERSON> on 7/25/25.
//

import Foundation
import Vapor
import SotoCore
import SotoCloudWatchLogs

// MARK: - SMS Reminder Logging Service

public class SMSReminderLogger {
    private let logger: Logger
    private let cloudWatchLogs: CloudWatchLogs?
    private let logGroupName: String
    private let logStreamName: String
    
    init(logger: Logger, client: AWSClient? = nil) {
        self.logger = logger
        // Use configurable region, defaulting to us-east-2 to match Lambda deployment
        let regionString = Environment.get("SMS_REMINDER_AWS_REGION") ?? "us-east-2"
        let region = Region(rawValue: regionString)
        self.cloudWatchLogs = client == nil ? CloudWatchLogs(client: client!, region: region) : nil
        self.logGroupName = Environment.get("SMS_REMINDER_LOG_GROUP") ?? "/aws/lambda/sms-reminder-handler"
        self.logStreamName = "sms-reminder-\(DateFormatter.logStreamFormatter.string(from: Date()))"
    }
    
    // MARK: - Logging Methods
    
    func logReminderScheduled(
        entityType: SMSReminderPayload.ReminderType,
        entityId: UUID,
        phoneNumber: String,
        scheduledDate: Date,
        memberName: String
    ) {
        let message = "SMS reminder scheduled"
        let metadata: [String: String] = [
            "action": "reminder_scheduled",
            "entity_type": entityType.rawValue,
            "entity_id": entityId.uuidString,
            "phone_number": maskPhoneNumber(phoneNumber),
            "scheduled_date": ISO8601DateFormatter().string(from: scheduledDate),
            "member_name": memberName
        ]
        
        logEvent(level: .info, message: message, metadata: metadata)
    }
    
    func logReminderCancelled(
        entityType: SMSReminderPayload.ReminderType,
        entityId: UUID,
        reason: String = "Entity completed or cancelled"
    ) {
        let message = "SMS reminder cancelled"
        let metadata: [String: String] = [
            "action": "reminder_cancelled",
            "entity_type": entityType.rawValue,
            "entity_id": entityId.uuidString,
            "reason": reason
        ]
        
        logEvent(level: .info, message: message, metadata: metadata)
    }
    
    func logSchedulingError(
        entityType: SMSReminderPayload.ReminderType,
        entityId: UUID,
        error: Error,
        phoneNumber: String? = nil
    ) {
        let message = "SMS reminder scheduling failed"
        var metadata: [String: String] = [
            "action": "scheduling_error",
            "entity_type": entityType.rawValue,
            "entity_id": entityId.uuidString,
            "error": error.localizedDescription,
            "error_type": String(describing: type(of: error))
        ]
        
        if let phoneNumber = phoneNumber {
            metadata["phone_number"] = maskPhoneNumber(phoneNumber)
        }
        
        logEvent(level: .error, message: message, metadata: metadata)
    }
    
    func logCancellationError(
        entityType: SMSReminderPayload.ReminderType,
        entityId: UUID,
        error: Error
    ) {
        let message = "SMS reminder cancellation failed"
        let metadata: [String: String] = [
            "action": "cancellation_error",
            "entity_type": entityType.rawValue,
            "entity_id": entityId.uuidString,
            "error": error.localizedDescription,
            "error_type": String(describing: type(of: error))
        ]
        
        logEvent(level: .error, message: message, metadata: metadata)
    }
    
    func logLambdaInvocation(
        entityType: SMSReminderPayload.ReminderType,
        entityId: UUID,
        phoneNumber: String,
        messageLength: Int
    ) {
        let message = "Lambda function invoked for SMS delivery"
        let metadata: [String: String] = [
            "action": "lambda_invoked",
            "entity_type": entityType.rawValue,
            "entity_id": entityId.uuidString,
            "phone_number": maskPhoneNumber(phoneNumber),
            "message_length": String(messageLength)
        ]
        
        logEvent(level: .info, message: message, metadata: metadata)
    }
    
    func logSystemMetrics(
        totalScheduled: Int,
        totalCancelled: Int,
        totalErrors: Int,
        timeWindow: String
    ) {
        let message = "SMS reminder system metrics"
        let metadata: [String: String] = [
            "action": "system_metrics",
            "total_scheduled": String(totalScheduled),
            "total_cancelled": String(totalCancelled),
            "total_errors": String(totalErrors),
            "time_window": timeWindow,
            "success_rate": String(format: "%.2f", Double(totalScheduled - totalErrors) / Double(max(totalScheduled, 1)) * 100)
        ]
        
        logEvent(level: .info, message: message, metadata: metadata)
    }
    
    // MARK: - Private Helper Methods
    
    private func logEvent(level: Logger.Level, message: String, metadata: [String: String]) {
        // Log to Vapor logger
        let data = metadata.compactMapValues({ Logger.MetadataValue.string($0) })
        logger.log(level: level, "\(message)", metadata: data)
        
        // Send to CloudWatch if available
        sendToCloudWatch(level: level, message: message, metadata: metadata)
    }
    
    private func sendToCloudWatch(level: Logger.Level, message: String, metadata: [String: String]) {
        guard let cloudWatchLogs = cloudWatchLogs else { return }
        
        let timestamp = Int64(Date().timeIntervalSince1970 * 1000)
        let logMessage = formatCloudWatchMessage(level: level, message: message, metadata: metadata)
        
        let logEvent = CloudWatchLogs.InputLogEvent(
            message: logMessage, timestamp: timestamp
        )
        
        let putLogEventsInput = CloudWatchLogs.PutLogEventsRequest(
            logEvents: [logEvent],
            logGroupName: logGroupName,
            logStreamName: logStreamName
        )
        
        // Send asynchronously without blocking
        Task {
            do {
                _ = try await cloudWatchLogs.putLogEvents(putLogEventsInput)
            } catch {
                logger.error("Failed to send log to CloudWatch: \(error)")
            }
        }
    }
    
    private func formatCloudWatchMessage(level: Logger.Level, message: String, metadata: [String: String]) -> String {
        let timestamp = ISO8601DateFormatter().string(from: Date())
        let levelString = level.rawValue.uppercased()
        
        var logLine = "[\(timestamp)] [\(levelString)] \(message)"
        
        if !metadata.isEmpty {
            let metadataString = metadata.map { "\($0.key)=\($0.value)" }.joined(separator: " ")
            logLine += " | \(metadataString)"
        }
        
        return logLine
    }
    
    private func maskPhoneNumber(_ phoneNumber: String) -> String {
        // Mask phone number for privacy: +1555***4567
        guard phoneNumber.count > 6 else { return "***" }
        
        let start = phoneNumber.prefix(4)
        let end = phoneNumber.suffix(4)
        let masked = String(repeating: "*", count: max(0, phoneNumber.count - 8))
        
        return "\(start)\(masked)\(end)"
    }
}

// MARK: - Error Types

enum SMSReminderError: Error, LocalizedError {
    case missingPhoneNumber
    case invalidPhoneNumber(String)
    case missingRequiredData(String)
    case awsSchedulerError(Error)
    case lambdaInvocationError(Error)
    case configurationError(String)
    case rateLimitExceeded
    case twilioError(String)
    
    var errorDescription: String? {
        switch self {
        case .missingPhoneNumber:
            return "Phone number is required for SMS reminders"
        case .invalidPhoneNumber(let number):
            return "Invalid phone number format: \(number)"
        case .missingRequiredData(let field):
            return "Missing required data: \(field)"
        case .awsSchedulerError(let error):
            return "AWS EventBridge Scheduler error: \(error.localizedDescription)"
        case .lambdaInvocationError(let error):
            return "Lambda function invocation error: \(error.localizedDescription)"
        case .configurationError(let message):
            return "Configuration error: \(message)"
        case .rateLimitExceeded:
            return "SMS reminder rate limit exceeded"
        case .twilioError(let message):
            return "Twilio API error: \(message)"
        }
    }
}

// MARK: - Metrics Collection

struct SMSReminderMetrics {
    var scheduledCount: Int = 0
    var cancelledCount: Int = 0
    var errorCount: Int = 0
    var lastResetTime: Date = Date()
    
    mutating func recordScheduled() {
        scheduledCount += 1
    }
    
    mutating func recordCancelled() {
        cancelledCount += 1
    }
    
    mutating func recordError() {
        errorCount += 1
    }
    
    mutating func reset() {
        scheduledCount = 0
        cancelledCount = 0
        errorCount = 0
        lastResetTime = Date()
    }
    
    var successRate: Double {
        let total = max(scheduledCount, 1)
        return Double(scheduledCount - errorCount) / Double(total) * 100
    }
}

// MARK: - Date Formatter Extension

extension DateFormatter {
    static let logStreamFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd-HH"
        formatter.timeZone = TimeZone(identifier: "UTC")
        return formatter
    }()
}

// MARK: - Application Extension

public extension Application {
    var smsReminderLogger: SMSReminderLogger {
        return SMSReminderLogger(logger: self.logger, client: self.aws.client)
    }
}

public extension Request {
    var smsReminderLogger: SMSReminderLogger {
        return SMSReminderLogger(logger: self.logger, client: self.application.aws.client)
    }
}
